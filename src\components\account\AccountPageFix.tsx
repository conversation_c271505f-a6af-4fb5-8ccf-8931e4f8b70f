/**
 * AccountPageFix Component
 * 
 * This component specializes in fixing issues with the My Account page
 * by ensuring proper Clerk-Supabase integration and user profile loading.
 * Updated to use the native integration approach.
 */

import React, { useEffect, useState } from 'react';
import { useUser, useAuth, useSession } from '@clerk/clerk-react';
import { createClerkSupabaseClient } from '../../lib/clerk-supabase-official';

interface AccountPageFixProps {
  children: React.ReactNode;
}

export default function AccountPageFix({ children }: AccountPageFixProps) {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const { session } = useSession();
  const [isFixed, setIsFixed] = useState(false);
  const [isCheckingProfile, setIsCheckingProfile] = useState(false);
  const [profileError, setProfileError] = useState<string | null>(null);

  useEffect(() => {
    // Only run if the user is loaded and signed in
    if (!isLoaded || !isSignedIn || !user || !session) {
      return;
    }

    const fixAccountIssues = async () => {
      console.log('AccountPageFix: Running account page fixes');
      setIsCheckingProfile(true);
      
      try {
        // Create a Supabase client using the native integration
        console.log('AccountPageFix: Creating Supabase client with native integration');
        const supabase = await createClerkSupabaseClient(session);
        
        // Log successful client creation
        console.log('AccountPageFix: Supabase client created successfully');
        
        // Test the client with a query on user_profiles table
        try {
          const { data: testData, error: testError } = await supabase
            .from('user_profiles')
            .select('*')
            .limit(1);
            
          console.log('AccountPageFix: Test query result (user_profiles table):', testData, testError);
          
          if (testError) {
            console.warn('AccountPageFix: Error querying user_profiles table:', testError);
            if (testError.code === '22P02' && testError.message.includes('invalid input syntax for type uuid')) {
              console.warn('UUID format error detected. This may indicate a mismatch between UUID and Clerk ID formats.');
            }
          }
        } catch (testErr) {
          console.error('AccountPageFix: Test query failed:', testErr);
          // Continue anyway
        }
        
        // Check if the user profile exists in Supabase
        console.log('AccountPageFix: Checking user profile for clerk_id:', user.id);
        const { data: profile, error: profileError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('clerk_id', user.id)
          .maybeSingle();
          
        if (profileError) {
          console.error('AccountPageFix: Error checking user profile:', profileError);
          throw new Error(`Profile check failed: ${profileError.message}`);
        }
        
        // If profile doesn't exist, create it
        if (!profile) {
          console.log('AccountPageFix: Creating user profile');
          const { error: createError } = await supabase
            .from('user_profiles')
            .insert({
              clerk_id: user.id,
              email: user.primaryEmailAddress?.emailAddress || '',
              first_name: user.firstName || '',
              last_name: user.lastName || '',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              role: 'guest',
              is_host: false
            });
            
          if (createError) {
            console.error('AccountPageFix: Error creating user profile:', createError);
            throw new Error(`Profile creation failed: ${createError.message}`);
          }
        } else {
          console.log('AccountPageFix: User profile exists');
        }
        
        // Ensure the profile data is up to date
        const { error: updateError } = await supabase
          .from('user_profiles')
          .update({
            email: user.primaryEmailAddress?.emailAddress || '',
            first_name: user.firstName || (profile?.first_name || ''),
            last_name: user.lastName || (profile?.last_name || ''),
            updated_at: new Date().toISOString()
          })
          .eq('clerk_id', user.id);
          
        if (updateError) {
          console.warn('AccountPageFix: Error updating profile:', updateError);
          // Non-fatal error, continue
        }
        
        // Try another basic query to ensure everything is working
        const { error: testError } = await supabase
          .from('user_profiles')
          .select('count')
          .limit(1);
          
        if (testError) {
          console.warn('AccountPageFix: Test query failed:', testError);
          // Non-fatal error, continue
        }
        
        console.log('AccountPageFix: All fixes applied successfully');
        setIsFixed(true);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('AccountPageFix: Failed to fix account issues:', errorMessage);
        setProfileError(errorMessage);
        
        // Still mark as fixed so the user can proceed
        setIsFixed(true);
      } finally {
        setIsCheckingProfile(false);
      }
    };
    
    fixAccountIssues();
  }, [isLoaded, isSignedIn, user, session]);
  
  // If not loaded yet, show loading
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }
  
  // If checking profile, show loading
  if (isCheckingProfile) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
          <p className="text-gray-600 mb-2">Preparing your account...</p>
          <p className="text-gray-400 text-sm">This will only take a moment</p>
        </div>
      </div>
    );
  }
  
  // If not signed in, show sign in button
  if (!isSignedIn) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-white shadow-md rounded-lg p-8 max-w-md text-center">
          <h2 className="text-2xl font-bold mb-4">Please Sign In</h2>
          <p className="text-gray-600 mb-6">You need to be signed in to access your account.</p>
          <a href="/login" className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-md inline-block">
            Sign In
          </a>
        </div>
      </div>
    );
  }
  
  // If fixed (or attempted to fix), render children
  if (isFixed) {
    return (
      <>
        {profileError && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  There was an issue loading your profile, but you can still continue. 
                  If you experience problems, please refresh the page.
                </p>
              </div>
            </div>
          </div>
        )}
        {children}
      </>
    );
  }
  
  // Default loading state
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="flex flex-col items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
        <p className="text-gray-600">Loading your account...</p>
      </div>
    </div>
  );
}
