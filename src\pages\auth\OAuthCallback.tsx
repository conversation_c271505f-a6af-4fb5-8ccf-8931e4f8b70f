import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useUser, useClerk } from '@clerk/clerk-react';
import { isPreregisteredHost } from '../../data/preregisteredHosts';
import { isHost } from '../../utils/user-roles';
import { simulateWebhookEvent } from '../../api/clerk-webhook-handler';
import { CLERK_CONFIG } from '../../config/clerk';
import { useSupabase } from '../../providers/SupabaseProvider';
import { syncUserWithSupabase, UserRole } from '../../services/auth';

export default function OAuthCallback() {
  const { user, isLoaded } = useUser();
  const { client } = useClerk();
  const navigate = useNavigate();
  const location = useLocation();
  const { setUserRole } = useSupabase();
  const [status, setStatus] = useState('Processing your sign-in...');
  const [error, setError] = useState('');
  const [processingComplete, setProcessingComplete] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 5;

  // Single useEffect to handle OAuth callback
  useEffect(() => {
    let isMounted = true;
    let retryTimeout: NodeJS.Timeout;

    const processOAuthCallback = async () => {
      console.log('OAuthCallback: Component mounted');
      console.log('OAuthCallback: User state:', { user, isLoaded });
      console.log('OAuthCallback: Current URL:', window.location.href);

      // If already processing or completed, don't process again
      if (processingComplete) {
        console.log('OAuthCallback: Already processed, skipping');
        return;
      }

      // Wait for Clerk to load
      if (!isLoaded) {
        console.log('OAuthCallback: Clerk not loaded yet, waiting...');
        return;
      }

      // If no user after Clerk is loaded, handle the error
      if (!user) {
        console.log('OAuthCallback: No user data available yet');
        
        // Retry logic with exponential backoff
        if (retryCount < maxRetries && isMounted) {
          const delay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Max 10 seconds
          console.log(`OAuthCallback: Retrying in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`);
          
          retryTimeout = setTimeout(() => {
            if (isMounted) {
              setRetryCount(prev => prev + 1);
            }
          }, delay);
          return;
        } else {
          // Max retries reached, show error
          console.warn('OAuthCallback: Max retries reached, redirecting to sign-in');
          setError('Authentication failed. Please try signing in again.');
          setStatus('Authentication failed');
          setTimeout(() => {
            if (isMounted) {
              navigate('/sign-in');
            }
          }, 3000);
          return;
        }
      }

      // User is available, process the authentication
      try {
        console.log('OAuthCallback: Processing user authentication');
        setStatus('Setting up your account...');

        // Check if registering as host
        const registeringAsHost = localStorage.getItem('registering_as_host') === 'true';
        const userEmail = user.primaryEmailAddress?.emailAddress;

        let userRole: UserRole = 'customer';
        let redirectPath = '/my-account';

        if (registeringAsHost) {
          console.log('OAuthCallback: User registering as host');
          userRole = 'host';
          redirectPath = '/host/dashboard';
          localStorage.removeItem('registering_as_host');
        } else if (userEmail && isPreregisteredHost(userEmail)) {
          console.log('OAuthCallback: User is preregistered host');
          userRole = 'host';
          redirectPath = '/host/dashboard';
        } else if (userEmail && isHost(userEmail)) {
          console.log('OAuthCallback: User is existing host');
          userRole = 'host';
          redirectPath = '/host/dashboard';
        }

        // Set user role in context
        setUserRole(userRole);

        // Sync with Supabase
        console.log('OAuthCallback: Syncing user with Supabase');
        await syncUserWithSupabase(user, userRole);

        // Simulate webhook event for consistency
        try {
          await simulateWebhookEvent({
            type: 'user.created',
            data: {
              id: user.id,
              email_addresses: user.emailAddresses,
              first_name: user.firstName,
              last_name: user.lastName,
              created_at: user.createdAt
            }
          });
        } catch (webhookError) {
          console.warn('OAuthCallback: Webhook simulation failed:', webhookError);
          // Don't fail the entire process for webhook errors
        }

        // Mark as complete and redirect
        setProcessingComplete(true);
        setStatus('Authentication successful! Redirecting...');
        
        console.log(`OAuthCallback: Redirecting to ${redirectPath}`);
        setTimeout(() => {
          if (isMounted) {
            navigate(redirectPath);
          }
        }, 1500);

      } catch (error) {
        console.error('OAuthCallback: Error processing authentication:', error);
        setError('Failed to complete authentication. Please try again.');
        setStatus('Authentication failed');
        
        setTimeout(() => {
          if (isMounted) {
            navigate('/sign-in');
          }
        }, 3000);
      }
    };

    processOAuthCallback();

    // Cleanup function
    return () => {
      isMounted = false;
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }
    };
  }, [user, isLoaded, retryCount, processingComplete, navigate, setUserRole]);

  // Render loading/error UI
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
        {error ? (
          <>
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Authentication Error</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="text-sm text-gray-500">
              Redirecting to sign-in page...
            </div>
          </>
        ) : (
          <>
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-600 mx-auto mb-6"></div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Almost There!</h1>
            <p className="text-gray-600 mb-4">{status}</p>
            {retryCount > 0 && (
              <div className="text-sm text-gray-500">
                Retry attempt {retryCount}/{maxRetries}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
