import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useUser, useClerk } from '@clerk/clerk-react';
import { isPreregisteredHost } from '../../data/preregisteredHosts';
import { isHost } from '../../utils/user-roles';
import { simulateWebhookEvent } from '../../api/clerk-webhook-handler';
import { CLERK_CONFIG } from '../../config/clerk';
import { handleOAuthCallback as handleDirectOAuthCallback } from '../../utils/clerk-oauth';
import { useSupabase } from '../../providers/SupabaseProvider';
import { syncUserWithSupabase, UserRole } from '../../services/auth';

export default function OAuthCallback() {
  const { user, isLoaded } = useUser();
  const { client } = useClerk();
  const navigate = useNavigate();
  const location = useLocation();
  const { setUserRole } = useSupabase();
  const [status, setStatus] = useState('Processing your sign-in...');
  const [error, setError] = useState('');
  const [processingComplete, setProcessingComplete] = useState(false);
  const [authAttemptsCount, setAuthAttemptsCount] = useState(0);

  // Check if we're registering as a host from localStorage and handle OAuth callback
  useEffect(() => {
    // Log all available information for debugging
    console.log('OAuthCallback: Component mounted');
    console.log('OAuthCallback: User state:', { user, isLoaded });
    console.log('OAuthCallback: Current URL:', window.location.href);
    console.log('OAuthCallback: Search params:', location.search);
    console.log('OAuthCallback: Pathname:', location.pathname);
    console.log('OAuthCallback: Clerk config:', {
      redirectUrl: CLERK_CONFIG.oauthCallbackURL,
      redirectUrls: CLERK_CONFIG.redirectUrls
    });

    // Log any stored OAuth attempt information
    const lastOAuthAttempt = sessionStorage.getItem('last_oauth_attempt');
    if (lastOAuthAttempt) {
      console.log('Last OAuth attempt:', JSON.parse(lastOAuthAttempt));
    }

    // Log any stored Clerk navigation information
    const lastClerkNavigation = sessionStorage.getItem('last_clerk_navigation');
    if (lastClerkNavigation) {
      console.log('Last Clerk navigation:', JSON.parse(lastClerkNavigation));
    }

    // Check if we're registering as a host
    const registeringAsHost = localStorage.getItem('registering_as_host') === 'true';
    if (registeringAsHost) {
      console.log('OAuthCallback: Found registering_as_host flag in localStorage');
      setStatus('Detected host registration from previous step...');
    }

    // Check for error parameters in the URL
    const params = new URLSearchParams(location.search);
    if (params.has('error') || params.has('error_description')) {
      const error = params.get('error');
      const errorDescription = params.get('error_description');
      console.error('OAuth error detected in URL:', { error, errorDescription });
      setError(`Authentication error: ${errorDescription || error || 'Unknown error'}`);
      return;
    }

    // Handle Google OAuth callback specifically
    const isGoogleCallback =
      location.pathname.includes('google') ||
      location.search.includes('google') ||
      location.search.includes('oauth_token') ||
      location.search.includes('code=') ||
      document.referrer.includes('google') ||
      document.referrer.includes('accounts.google.com');

    if (isGoogleCallback) {
      console.log('Google OAuth callback detected');
      setStatus('Processing your Google sign-in...');

      // Store a flag to indicate we're in a Google OAuth flow
      localStorage.setItem('google_oauth_flow', 'true');
      localStorage.setItem('google_oauth_timestamp', new Date().toISOString());

      // If we're in development mode and this is a Google callback, we might need to
      // manually handle the OAuth flow completion
      if (CLERK_CONFIG.developmentMode) {
        console.log('Development mode detected, preparing fallback handling for Google OAuth');

        // In development mode, we can simulate a successful sign-in
        const registeringAsHost = localStorage.getItem('registering_as_host') === 'true';

        // Set user role in localStorage for development mode
        localStorage.setItem('user_role', registeringAsHost ? 'host' : 'guest');
        localStorage.setItem('user_email', '<EMAIL>');

        // Redirect to the appropriate page after a short delay
        setTimeout(() => {
          if (registeringAsHost) {
            navigate('/host/dashboard?auth=success&dev_mode=true');
          } else {
            navigate('/?auth=success&dev_mode=true');
          }
          setProcessingComplete(true);
        }, 1500);
      }
    }

    // If this is an SSO callback, extract the parameters and store them
    if (location.pathname === '/sso-callback') {
      const params = new URLSearchParams(location.search);
      const afterSignInUrl = params.get('after_sign_in_url');
      const afterSignUpUrl = params.get('after_sign_up_url');
      const redirectUrl = params.get('redirect_url');

      console.log('SSO Callback detected with params:', {
        afterSignInUrl,
        afterSignUpUrl,
        redirectUrl
      });

      // Store these parameters for later use
      if (afterSignInUrl) localStorage.setItem('after_sign_in_url', afterSignInUrl);
      if (afterSignUpUrl) localStorage.setItem('after_sign_up_url', afterSignUpUrl);
      if (redirectUrl) localStorage.setItem('redirect_url', redirectUrl);
    }

    // If we don't have user data yet, show a loading message
    if (!isLoaded) {
      console.log('OAuthCallback: Clerk not loaded yet, waiting...');
      setStatus('Initializing authentication, please wait...');
    } else if (!user) {
      console.log('OAuthCallback: No user data available yet');
      setStatus('Processing your sign-in, please wait...');
    } else {
      console.log('OAuthCallback: User authenticated:', {
        id: user.id,
        email: user.primaryEmailAddress?.emailAddress
      });
      setStatus('Authentication successful, redirecting...');
    }
  }, [location, isLoaded, user]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  useEffect(() => {
    // Wait for user data to load
    if (!isLoaded && !processingComplete) {
      console.log('Waiting for user data to load...');
      return;
    }

    // If we've been waiting too long for user data, implement a fallback
    const googleOAuthFlow = localStorage.getItem('google_oauth_flow') === 'true' ||
                         location.pathname.includes('google') ||
                         location.search.includes('google') ||
                         location.search.includes('oauth_token') ||
                         location.search.includes('code=');

    if (!user && googleOAuthFlow) {
      console.warn('Fallback triggered: No user data available after Google OAuth callback');
      setStatus('Authentication delayed, retrying...');

      // Retry logic for delayed user data
      const retryInterval = setInterval(() => {
        if (user) {
          console.log('User data loaded after fallback:', {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          });
          setStatus('Authentication successful, redirecting...');
          clearInterval(retryInterval);
          navigate('/dashboard');
        } else {
          console.log('Retrying user data fetch...');
        }
      }, 2000); // Retry every 2 seconds

      // Stop retrying after 10 attempts
      setTimeout(() => {
        clearInterval(retryInterval);
        console.error('Failed to load user data after multiple retries');
        setStatus('Authentication failed, please try again later.');
      }, 20000); // 20 seconds timeout
    }
  }, [isLoaded, user, processingComplete, location]);

  return (
    <div>
      <h1>OAuth Callback</h1>
      <p>{status}</p>
      {error && <p style={{ color: 'red' }}>{error}</p>}
    </div>
  );
}