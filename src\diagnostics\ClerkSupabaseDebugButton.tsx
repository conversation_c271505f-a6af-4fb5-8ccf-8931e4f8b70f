import React from 'react';

export default function ClerkSupabaseDebugButton() {
  const runDiagnostics = async () => {
    try {
      console.log('Running Clerk-Supabase diagnostics...');
      
      // Check Clerk state
      const clerkState = {
        loaded: typeof window !== 'undefined' && !!window.Clerk,
        hasSession: typeof window !== 'undefined' && !!window.Clerk?.session,
        userId: typeof window !== 'undefined' && window.Clerk?.user?.id,
        sessionId: typeof window !== 'undefined' && window.Clerk?.session?.id
      };
      
      console.log('Clerk state:', clerkState);
      
      // If we have a session, try to get a token
      let token = null;
      if (clerkState.hasSession) {
        try {
          token = await window.Clerk.session.getToken();
          console.log('Clerk token available:', !!token);
        } catch (tokenError) {
          console.error('Error getting Clerk token:', tokenError);
        }
      }
      
      // Try fixing any issues
      if (clerkState.hasSession && token) {
        console.log('Auth looks good! Trying to fix any Supabase issues...');
        
        try {
          // Force reload the page
          window.location.reload();
        } catch (e) {
          console.error('Error during fix attempt:', e);
        }
      } else {
        alert('Authentication issue detected. Please try logging out and back in.');
      }
    } catch (error) {
      console.error('Error running diagnostics:', error);
    }
  };
  
  return (
    <button
      onClick={runDiagnostics}
      className="fixed bottom-4 right-4 bg-indigo-600 text-white px-4 py-2 rounded-md shadow-md z-50"
    >
      Debug Auth
    </button>
  );
}
