#!/usr/bin/env node

/**
 * Static Page Generator for SEO
 * 
 * This script generates static HTML files for critical pages to ensure
 * Google can crawl and index them properly, fixing the Soft 404 issues.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const publicDir = path.resolve(__dirname, '../public');

// Base URL for the website
const baseUrl = 'https://housegoing.com.au';

// Critical pages that need static HTML for SEO
const criticalPages = [
  {
    path: '/contact',
    title: 'Contact Us | HouseGoing',
    description: 'Get in touch with HouseGoing for venue rental inquiries, support, or partnership opportunities in NSW, Australia.',
    content: `
      <main class="pt-32 px-4 sm:px-6 max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold text-gray-900 mb-8">Contact HouseGoing</h1>
        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h2 class="text-2xl font-semibold mb-4">Get in Touch</h2>
            <p class="text-gray-600 mb-6">Have questions about venue rentals, hosting, or our platform? We're here to help!</p>
            <div class="space-y-4">
              <div>
                <h3 class="font-semibold">Email</h3>
                <p><EMAIL></p>
              </div>
              <div>
                <h3 class="font-semibold">Phone</h3>
                <p>1300 HOUSE GO (1300 468 734)</p>
              </div>
              <div>
                <h3 class="font-semibold">Business Hours</h3>
                <p>Monday - Friday: 9:00 AM - 6:00 PM AEST</p>
              </div>
            </div>
          </div>
          <div>
            <h2 class="text-2xl font-semibold mb-4">Quick Links</h2>
            <ul class="space-y-2">
              <li><a href="/find-venues" class="text-purple-600 hover:text-purple-700">Find Venues</a></li>
              <li><a href="/list-space" class="text-purple-600 hover:text-purple-700">List Your Space</a></li>
              <li><a href="/faq" class="text-purple-600 hover:text-purple-700">FAQ</a></li>
              <li><a href="/help" class="text-purple-600 hover:text-purple-700">Help Center</a></li>
            </ul>
          </div>
        </div>
      </main>
    `
  },
  {
    path: '/help',
    title: 'Help Center | HouseGoing',
    description: 'Find answers to common questions about venue booking, hosting, and using the HouseGoing platform.',
    content: `
      <main class="pt-32 px-4 sm:px-6 max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold text-gray-900 mb-8">Help Center</h1>
        <div class="space-y-8">
          <section>
            <h2 class="text-2xl font-semibold mb-4">Frequently Asked Questions</h2>
            <div class="space-y-4">
              <div>
                <h3 class="font-semibold">How do I book a venue?</h3>
                <p class="text-gray-600">Browse our venue listings, select your preferred space, choose your dates, and complete the booking process.</p>
              </div>
              <div>
                <h3 class="font-semibold">What are the noise restrictions in NSW?</h3>
                <p class="text-gray-600">NSW has specific noise regulations for residential areas. Check our NSW Party Planning guide for detailed information.</p>
              </div>
              <div>
                <h3 class="font-semibold">How do I list my property?</h3>
                <p class="text-gray-600">Visit our "List Your Space" page to start the hosting process and earn income from your property.</p>
              </div>
            </div>
          </section>
          <section>
            <h2 class="text-2xl font-semibold mb-4">Need More Help?</h2>
            <p class="text-gray-600 mb-4">Can't find what you're looking for? Contact our support team:</p>
            <a href="/contact" class="inline-block bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700">Contact Support</a>
          </section>
        </div>
      </main>
    `
  },
  {
    path: '/safety',
    title: 'Safety Guidelines | HouseGoing',
    description: 'Learn about safety guidelines, insurance requirements, and best practices for hosting and attending events through HouseGoing.',
    content: `
      <main class="pt-32 px-4 sm:px-6 max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold text-gray-900 mb-8">Safety Guidelines</h1>
        <div class="space-y-8">
          <section>
            <h2 class="text-2xl font-semibold mb-4">Host Safety Requirements</h2>
            <ul class="list-disc list-inside space-y-2 text-gray-600">
              <li>Maintain valid public liability insurance (minimum $10 million recommended)</li>
              <li>Ensure all safety equipment is functional (smoke alarms, fire extinguishers)</li>
              <li>Provide clear emergency exit information</li>
              <li>Comply with local council noise regulations</li>
              <li>Maintain property in safe, clean condition</li>
            </ul>
          </section>
          <section>
            <h2 class="text-2xl font-semibold mb-4">Guest Responsibilities</h2>
            <ul class="list-disc list-inside space-y-2 text-gray-600">
              <li>Respect property rules and noise restrictions</li>
              <li>Report any safety concerns immediately</li>
              <li>Follow capacity limits</li>
              <li>Clean up after events</li>
              <li>Treat the property with care</li>
            </ul>
          </section>
          <section>
            <h2 class="text-2xl font-semibold mb-4">Emergency Procedures</h2>
            <p class="text-gray-600 mb-4">In case of emergency:</p>
            <ul class="list-disc list-inside space-y-2 text-gray-600">
              <li>Call 000 for immediate emergency services</li>
              <li>Contact the property host</li>
              <li>Report incidents to HouseGoing support</li>
            </ul>
          </section>
        </div>
      </main>
    `
  },
  {
    path: '/cookies',
    title: 'Cookie Policy | HouseGoing',
    description: 'Learn about how HouseGoing uses cookies to improve your browsing experience and provide personalized services.',
    content: `
      <main class="pt-32 px-4 sm:px-6 max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold text-gray-900 mb-8">Cookie Policy</h1>
        <div class="space-y-6">
          <section>
            <h2 class="text-2xl font-semibold mb-4">What Are Cookies?</h2>
            <p class="text-gray-600">Cookies are small text files stored on your device when you visit our website. They help us provide you with a better browsing experience.</p>
          </section>
          <section>
            <h2 class="text-2xl font-semibold mb-4">How We Use Cookies</h2>
            <ul class="list-disc list-inside space-y-2 text-gray-600">
              <li>Essential cookies for website functionality</li>
              <li>Analytics cookies to understand user behavior</li>
              <li>Preference cookies to remember your settings</li>
              <li>Marketing cookies for personalized advertising</li>
            </ul>
          </section>
          <section>
            <h2 class="text-2xl font-semibold mb-4">Managing Cookies</h2>
            <p class="text-gray-600">You can control cookies through your browser settings. Note that disabling certain cookies may affect website functionality.</p>
          </section>
          <p class="text-sm text-gray-500">Last updated: January 2025</p>
        </div>
      </main>
    `
  }
];

// Generate HTML template
function generateHTML(page) {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${page.title}</title>
    <meta name="description" content="${page.description}">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="${baseUrl}${page.path}">
    
    <!-- Open Graph -->
    <meta property="og:title" content="${page.title}">
    <meta property="og:description" content="${page.description}">
    <meta property="og:url" content="${baseUrl}${page.path}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="HouseGoing">
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="${page.title}">
    <meta name="twitter:description" content="${page.description}">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "${page.title}",
        "description": "${page.description}",
        "url": "${baseUrl}${page.path}",
        "isPartOf": {
            "@type": "WebSite",
            "@id": "${baseUrl}/#website"
        }
    }
    </script>
    
    <!-- Basic Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Redirect to SPA after page loads (for interactive features) -->
    <script>
        // Allow search engines to crawl the static content
        // Then redirect to SPA for interactive features
        if (!navigator.userAgent.includes('bot') && !navigator.userAgent.includes('crawler')) {
            setTimeout(() => {
                window.location.href = \`${page.path}\`;
            }, 2000);
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Static header -->
    <header class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-purple-600">HouseGoing</a>
                </div>
                <nav class="hidden md:flex space-x-8">
                    <a href="/find-venues" class="text-gray-700 hover:text-purple-600">Find Venues</a>
                    <a href="/list-space" class="text-gray-700 hover:text-purple-600">List Space</a>
                    <a href="/how-it-works" class="text-gray-700 hover:text-purple-600">How It Works</a>
                    <a href="/contact" class="text-gray-700 hover:text-purple-600">Contact</a>
                </nav>
            </div>
        </div>
    </header>
    
    ${page.content}
    
    <!-- Static footer -->
    <footer class="bg-gray-900 text-white mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">HouseGoing</h3>
                    <p class="text-gray-400">Find your perfect party venue in NSW, Australia.</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/find-venues">Find Venues</a></li>
                        <li><a href="/list-space">List Space</a></li>
                        <li><a href="/how-it-works">How It Works</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/help">Help Center</a></li>
                        <li><a href="/contact">Contact</a></li>
                        <li><a href="/safety">Safety</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/terms">Terms</a></li>
                        <li><a href="/privacy">Privacy</a></li>
                        <li><a href="/cookies">Cookies</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2025 HouseGoing Pty Ltd. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>`;
}

// Generate static HTML files
function generateStaticPages() {
    console.log('Generating static HTML pages for SEO...');
    
    // Ensure public directory exists
    if (!fs.existsSync(publicDir)) {
        fs.mkdirSync(publicDir, { recursive: true });
    }
    
    let generatedCount = 0;
    
    for (const page of criticalPages) {
        try {
            const html = generateHTML(page);
            const fileName = page.path === '/' ? 'index.html' : `${page.path.slice(1)}.html`;
            const filePath = path.join(publicDir, fileName);
            
            fs.writeFileSync(filePath, html, 'utf8');
            console.log(`✓ Generated: ${fileName}`);
            generatedCount++;
        } catch (error) {
            console.error(`✗ Failed to generate ${page.path}:`, error.message);
        }
    }
    
    console.log(`\n🎉 Successfully generated ${generatedCount} static HTML pages!`);
    console.log('\nThese pages will now be crawlable by search engines and should fix the Soft 404 issues.');
    console.log('\nNext steps:');
    console.log('1. Deploy these files to your server');
    console.log('2. Submit updated sitemap to Google Search Console');
    console.log('3. Request indexing for the fixed pages');
}

// Execute the function
generateStaticPages();
