<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HouseGoing | Find & Book Venues in NSW</title>
    <meta name="description" content="Browse, book, and manage party venues across NSW. Mobile-first, touch-friendly, and conversion-optimized.">
    <meta name="robots" content="noindex, nofollow">
    <link rel="canonical" href="https://housegoing.com.au/find-venues">
    <style>
      /* Mobile-first critical fixes */
      @media (max-width: 600px) {
        body {
          font-size: 16px;
          font-family: system-ui, sans-serif;
          margin: 0;
          padding: 0;
          background: #fafbfc;
        }
        h1, h2, h3 {
          font-size: 1.4em;
          margin: 0.7em 0 0.4em 0;
        }
        .mobile-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 1em;
          background: #fff;
          box-shadow: 0 2px 8px rgba(0,0,0,0.04);
          position: sticky;
          top: 0;
          z-index: 1002;
        }
        .mobile-menu-btn {
          width: 44px;
          height: 44px;
          background: none;
          border: none;
          font-size: 2em;
          cursor: pointer;
        }
        .mobile-menu {
          display: none;
          position: fixed;
          top: 0; left: 0; right: 0; bottom: 0;
          background: rgba(0,0,0,0.7);
          z-index: 1000;
        }
        .mobile-menu.open {
          display: block;
        }
        .mobile-menu-content {
          background: #fff;
          width: 80vw;
          max-width: 320px;
          height: 100vh;
          padding: 2em 1em;
          box-shadow: 2px 0 8px rgba(0,0,0,0.1);
        }
        .mobile-menu-content a, .mobile-menu-content button {
          display: block;
          padding: 1em 0;
          font-size: 1.1em;
          color: #222;
          text-decoration: none;
          background: none;
          border: none;
          text-align: left;
          width: 100%;
        }
        .mobile-menu-content .cta-btn {
          background: #1e90ff;
          color: #fff;
          border-radius: 8px;
          margin: 1em 0;
        }
        .cta-btn {
          display: block;
          width: 100%;
          background: #1e90ff;
          color: #fff;
          border: none;
          border-radius: 8px;
          padding: 1em;
          font-size: 1.1em;
          margin: 1em 0;
          min-height: 44px;
          cursor: pointer;
        }
        .sticky-action-bar {
          position: fixed;
          bottom: 0; left: 0; right: 0;
          background: #fff;
          box-shadow: 0 -2px 8px rgba(0,0,0,0.06);
          padding: 0.5em 1em;
          display: flex;
          gap: 1em;
          z-index: 1001;
        }
        .sticky-action-bar .cta-btn {
          margin: 0;
          flex: 1;
        }
        .mobile-section {
          padding: 1em;
        }
        .mobile-search {
          width: 100%;
          padding: 0.8em;
          font-size: 1em;
          border-radius: 8px;
          border: 1px solid #ddd;
          margin-bottom: 1em;
        }
        .mobile-filters {
          display: flex;
          gap: 0.5em;
          margin-bottom: 1em;
          flex-wrap: wrap;
        }
        .mobile-filters select, .mobile-filters input {
          font-size: 1em;
          padding: 0.5em;
          border-radius: 8px;
          border: 1px solid #ddd;
        }
        .venue-card {
          background: #fff;
          border-radius: 10px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.04);
          margin-bottom: 1em;
          padding: 1em;
        }
        .venue-card h2 {
          font-size: 1.2em;
        }
        .venue-card .cta-btn {
          font-size: 1em;
          padding: 0.7em;
        }
        .quick-links {
          display: flex;
          gap: 0.5em;
          margin: 1em 0;
          flex-wrap: wrap;
        }
        .quick-links a {
          background: #f0f4f8;
          color: #1e90ff;
          border-radius: 6px;
          padding: 0.5em 1em;
          font-size: 1em;
          text-decoration: none;
        }
        .map-link {
          display: inline-block;
          margin-top: 0.5em;
          color: #1e90ff;
          text-decoration: underline;
        }
      }
    </style>
</head>
<body>
    <div class="mobile-header">
      <span style="font-weight:bold; font-size:1.2em;">HouseGoing</span>
      <button class="mobile-menu-btn" aria-label="Open menu" onclick="document.querySelector('.mobile-menu').classList.add('open')">&#9776;</button>
    </div>
    <nav class="mobile-menu" onclick="this.classList.remove('open')">
      <div class="mobile-menu-content" onclick="event.stopPropagation()">
        <a href="/">Home</a>
        <a href="/find-venues">Find Venues</a>
        <a href="/about">About</a>
        <a href="/faq">FAQ</a>
        <a href="/support">Support</a>
        <a href="/contact">Contact</a>
        <a href="tel:+61123456789">Call Us</a>
        <button class="cta-btn" onclick="window.location.href='/book'">Book Now</button>
        <button class="cta-btn" onclick="window.location.href='/login'">Login</button>
      </div>
    </nav>
    <div class="mobile-section">
      <input class="mobile-search" type="search" placeholder="Search venues, locations..." aria-label="Search venues">
      <div class="mobile-filters">
        <select aria-label="Location">
          <option value="">All Locations</option>
          <option value="sydney">Sydney</option>
          <option value="newcastle">Newcastle</option>
          <option value="wollongong">Wollongong</option>
        </select>
        <select aria-label="Capacity">
          <option value="">Any Capacity</option>
          <option value="50">Up to 50</option>
          <option value="100">Up to 100</option>
          <option value="200">Up to 200</option>
        </select>
        <select aria-label="Price">
          <option value="">Any Price</option>
          <option value="low">$</option>
          <option value="mid">$$</option>
          <option value="high">$$$</option>
        </select>
      </div>
      <div class="quick-links">
        <a href="/popular">Popular</a>
        <a href="/new">New</a>
        <a href="/outdoor">Outdoor</a>
        <a href="/kids">Kids</a>
        <a href="/corporate">Corporate</a>
      </div>
      <div class="venue-card">
        <h2>Sample Venue Name</h2>
        <p>Location: Sydney CBD</p>
        <p>Capacity: 100</p>
        <a class="map-link" href="https://maps.google.com/?q=Sydney+CBD" target="_blank">View on Map</a>
        <button class="cta-btn" onclick="window.location.href='/venue/1'">View Details</button>
        <button class="cta-btn" onclick="window.location.href='/book?venue=1'">Book Now</button>
      </div>
      <!-- More venue cards dynamically loaded here -->
    </div>
    <div class="sticky-action-bar">
      <button class="cta-btn" onclick="window.location.href='/book'">Book a Venue</button>
      <button class="cta-btn" onclick="window.location.href='/contact'">Contact</button>
      <button class="cta-btn" onclick="window.location.href='tel:+61123456789'">Call</button>
    </div>
    <script>
      // If this file is loaded directly, redirect to the SPA root
      if (window.innerWidth > 600) {
        window.location.replace("/");
      }
    </script>
    <noscript>
      <h1>HouseGoing</h1>
      <p>This page requires JavaScript. Please enable JavaScript to use the full site.</p>
    </noscript>
</body>
</html>