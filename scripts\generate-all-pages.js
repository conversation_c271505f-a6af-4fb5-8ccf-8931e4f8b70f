#!/usr/bin/env node

/**
 * Complete Static Page Generator for ALL Sitemap URLs
 * 
 * This script generates static HTML files for EVERY URL in the sitemap
 * to ensure NO 404 errors and proper Google indexing.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const publicDir = path.resolve(__dirname, '../public');

// Base URL for the website
const baseUrl = 'https://housegoing.com.au';

// Extract all URLs from sitemap
function extractUrlsFromSitemap() {
  const sitemapPath = path.join(publicDir, 'sitemap_comprehensive.xml');
  const sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
  
  const urlMatches = sitemapContent.match(/<loc>(.*?)<\/loc>/g);
  if (!urlMatches) return [];
  
  return urlMatches.map(match => {
    const url = match.replace('<loc>', '').replace('</loc>', '');
    return url.replace(baseUrl, '') || '/';
  });
}

// Generate basic HTML template
function generateBasicHTML(urlPath, pageData) {
  const { title, description, content } = pageData;
  
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <meta name="description" content="${description}">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <link rel="canonical" href="${baseUrl}${urlPath}">
    
    <!-- Open Graph -->
    <meta property="og:title" content="${title}">
    <meta property="og:description" content="${description}">
    <meta property="og:url" content="${baseUrl}${urlPath}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="HouseGoing">
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="${title}">
    <meta name="twitter:description" content="${description}">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "${title}",
        "description": "${description}",
        "url": "${baseUrl}${urlPath}",
        "isPartOf": {
            "@type": "WebSite",
            "@id": "${baseUrl}/#website"
        }
    }
    </script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Auto-redirect to SPA for interactivity (after crawlers see content) -->
    <script>
        // Only redirect real users, not bots
        if (!navigator.userAgent.match(/bot|crawler|spider/i)) {
            setTimeout(() => {
                window.location.href = \`${urlPath}\`;
            }, 2000);
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-purple-600">HouseGoing</a>
                </div>
                <nav class="hidden md:flex space-x-8">
                    <a href="/find-venues" class="text-gray-700 hover:text-purple-600">Find Venues</a>
                    <a href="/list-space" class="text-gray-700 hover:text-purple-600">List Space</a>
                    <a href="/how-it-works" class="text-gray-700 hover:text-purple-600">How It Works</a>
                    <a href="/contact" class="text-gray-700 hover:text-purple-600">Contact</a>
                </nav>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main class="pt-20 px-4 sm:px-6 max-w-6xl mx-auto min-h-screen">
        ${content}
    </main>
    
    <!-- Footer -->
    <footer class="bg-gray-900 text-white mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">HouseGoing</h3>
                    <p class="text-gray-400">Find your perfect party venue in NSW, Australia.</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/find-venues">Find Venues</a></li>
                        <li><a href="/list-space">List Space</a></li>
                        <li><a href="/how-it-works">How It Works</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/help">Help Center</a></li>
                        <li><a href="/contact">Contact</a></li>
                        <li><a href="/safety">Safety</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/terms">Terms</a></li>
                        <li><a href="/privacy">Privacy</a></li>
                        <li><a href="/cookies">Cookies</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2025 HouseGoing Pty Ltd. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>`;
}

// Page content definitions
const pageDefinitions = {
  '/': {
    title: 'Find Your Perfect Party Venue | HouseGoing',
    description: 'HouseGoing helps you find the perfect venue for your next party or event in NSW, Australia. Browse venues, check noise restrictions, and book with confidence.',
    content: `
      <div class="text-center py-16">
        <h1 class="text-5xl font-bold text-gray-900 mb-6">Find Your Perfect Party Venue</h1>
        <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          Discover amazing venues for your next party or event in NSW, Australia. 
          From rooftop terraces to beachside villas, find the perfect space for any occasion.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/find-venues" class="bg-purple-600 text-white px-8 py-3 rounded-lg hover:bg-purple-700 transition-colors">
            Browse Venues
          </a>
          <a href="/list-space" class="border border-purple-600 text-purple-600 px-8 py-3 rounded-lg hover:bg-purple-50 transition-colors">
            List Your Space
          </a>
        </div>
      </div>
      <div class="grid md:grid-cols-3 gap-8 py-16">
        <div class="text-center">
          <h3 class="text-xl font-semibold mb-4">Easy Booking</h3>
          <p class="text-gray-600">Browse, compare, and book venues with confidence. Our platform makes it simple.</p>
        </div>
        <div class="text-center">
          <h3 class="text-xl font-semibold mb-4">Verified Venues</h3>
          <p class="text-gray-600">All venues are verified and comply with NSW noise regulations and safety standards.</p>
        </div>
        <div class="text-center">
          <h3 class="text-xl font-semibold mb-4">24/7 Support</h3>
          <p class="text-gray-600">Our team is here to help you plan the perfect event from start to finish.</p>
        </div>
      </div>
    `
  },
  
  '/find-venues': {
    title: 'Find Party Venues in NSW | HouseGoing',
    description: 'Browse and book amazing party venues across NSW. Filter by location, capacity, price, and amenities to find your perfect event space.',
    content: `
      <div class="py-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-6">Find Your Perfect Venue</h1>
        <p class="text-lg text-gray-600 mb-8">
          Browse our curated selection of party venues across NSW. From intimate gatherings to large celebrations.
        </p>
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 class="text-2xl font-semibold mb-4">Search Venues</h2>
          <p class="text-gray-600">Use our advanced search to find venues by location, capacity, price range, and amenities.</p>
          <a href="/find-venues" class="inline-block mt-4 bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700">
            Start Searching
          </a>
        </div>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="font-semibold mb-2">Sydney CBD Venues</h3>
            <p class="text-gray-600 text-sm mb-4">Premium venues in the heart of Sydney</p>
            <a href="/locations/sydney-cbd" class="text-purple-600 hover:text-purple-700">Explore →</a>
          </div>
          <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="font-semibold mb-2">Bondi Beach Venues</h3>
            <p class="text-gray-600 text-sm mb-4">Beachside venues with stunning views</p>
            <a href="/locations/bondi-beach" class="text-purple-600 hover:text-purple-700">Explore →</a>
          </div>
          <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="font-semibold mb-2">Parramatta Venues</h3>
            <p class="text-gray-600 text-sm mb-4">Modern venues in Western Sydney</p>
            <a href="/locations/parramatta" class="text-purple-600 hover:text-purple-700">Explore →</a>
          </div>
        </div>
      </div>
    `
  },

  '/contact': {
    title: 'Contact Us | HouseGoing',
    description: 'Get in touch with HouseGoing for venue rental inquiries, support, or partnership opportunities in NSW, Australia.',
    content: `
      <div class="py-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-8">Contact HouseGoing</h1>
        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h2 class="text-2xl font-semibold mb-4">Get in Touch</h2>
            <p class="text-gray-600 mb-6">Have questions about venue rentals, hosting, or our platform? We're here to help!</p>
            <div class="space-y-4">
              <div>
                <h3 class="font-semibold">Email</h3>
                <p><EMAIL></p>
              </div>
              <div>
                <h3 class="font-semibold">Phone</h3>
                <p>1300 HOUSE GO (1300 468 734)</p>
              </div>
              <div>
                <h3 class="font-semibold">Business Hours</h3>
                <p>Monday - Friday: 9:00 AM - 6:00 PM AEST</p>
              </div>
            </div>
          </div>
          <div>
            <h2 class="text-2xl font-semibold mb-4">Quick Links</h2>
            <ul class="space-y-2">
              <li><a href="/find-venues" class="text-purple-600 hover:text-purple-700">Find Venues</a></li>
              <li><a href="/list-space" class="text-purple-600 hover:text-purple-700">List Your Space</a></li>
              <li><a href="/faq" class="text-purple-600 hover:text-purple-700">FAQ</a></li>
              <li><a href="/help" class="text-purple-600 hover:text-purple-700">Help Center</a></li>
            </ul>
          </div>
        </div>
      </div>
    `
  },

  // Default template for other pages
  'default': {
    title: 'HouseGoing - Party Venue Rentals',
    description: 'Find and book amazing party venues across NSW, Australia. Professional venue rental platform with verified listings.',
    content: `
      <div class="py-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-6">HouseGoing</h1>
        <p class="text-lg text-gray-600 mb-8">
          Your premier destination for party venue rentals in NSW, Australia.
        </p>
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-2xl font-semibold mb-4">Explore Our Platform</h2>
          <p class="text-gray-600 mb-6">
            Discover amazing venues, plan your perfect event, and create unforgettable memories.
          </p>
          <div class="flex flex-col sm:flex-row gap-4">
            <a href="/find-venues" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 text-center">
              Browse Venues
            </a>
            <a href="/list-space" class="border border-purple-600 text-purple-600 px-6 py-3 rounded-lg hover:bg-purple-50 text-center">
              List Your Space
            </a>
          </div>
        </div>
      </div>
    `
  }
};

// Generate all static pages
function generateAllPages() {
  console.log('🚀 Generating static HTML for ALL sitemap URLs...');
  
  // Extract URLs from sitemap
  const urls = extractUrlsFromSitemap();
  console.log(`Found ${urls.length} URLs in sitemap`);
  
  let generatedCount = 0;
  let skippedCount = 0;
  
  for (const urlPath of urls) {
    try {
      // Get page data or use default
      const pageData = pageDefinitions[urlPath] || {
        ...pageDefinitions.default,
        title: `${urlPath.replace('/', '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} | HouseGoing`,
        description: `${urlPath.replace('/', '').replace(/-/g, ' ')} - Find and book amazing party venues on HouseGoing.`
      };
      
      // Generate HTML
      const html = generateBasicHTML(urlPath, pageData);
      
      // Determine file path
      let filePath;
      if (urlPath === '/') {
        filePath = path.join(publicDir, 'index.html');
      } else {
        const cleanPath = urlPath.replace(/^\//, '');
        if (cleanPath.includes('/')) {
          // Create directory structure
          const dirPath = path.join(publicDir, path.dirname(cleanPath));
          if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
          }
          filePath = path.join(publicDir, `${cleanPath}.html`);
        } else {
          filePath = path.join(publicDir, `${cleanPath}.html`);
        }
      }
      
      // Write file
      fs.writeFileSync(filePath, html, 'utf8');
      console.log(`✅ Generated: ${urlPath} → ${path.relative(publicDir, filePath)}`);
      generatedCount++;
      
    } catch (error) {
      console.error(`❌ Failed to generate ${urlPath}:`, error.message);
      skippedCount++;
    }
  }
  
  console.log(`\n🎉 Generation Complete!`);
  console.log(`✅ Successfully generated: ${generatedCount} pages`);
  console.log(`❌ Skipped due to errors: ${skippedCount} pages`);
  console.log(`📊 Total URLs processed: ${urls.length}`);
  
  console.log('\n📋 Next Steps:');
  console.log('1. Deploy these files to your production server');
  console.log('2. Test a few URLs to ensure they work');
  console.log('3. Submit updated sitemap to Google Search Console');
  console.log('4. Monitor for 404 error reduction');
}

// Execute the function
generateAllPages();
