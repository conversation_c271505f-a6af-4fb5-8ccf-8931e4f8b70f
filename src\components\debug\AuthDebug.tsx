import React from 'react';
import { useAuth, useUser, useSession } from '@clerk/clerk-react';

export default function AuthDebug() {
  const { isLoaded, isSignedIn, userId } = useAuth();
  const { user } = useUser();
  const { session } = useSession();

  // Only show in development
  if (import.meta.env.PROD) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">Auth Debug</h3>
      <div className="space-y-1">
        <div>isLoaded: {isLoaded ? '✅' : '❌'}</div>
        <div>isSignedIn: {isSignedIn ? '✅' : '❌'}</div>
        <div>userId: {userId || 'null'}</div>
        <div>user: {user ? '✅' : '❌'}</div>
        <div>session: {session ? '✅' : '❌'}</div>
        <div>pathname: {window.location.pathname}</div>
        <div>timestamp: {new Date().toLocaleTimeString()}</div>
      </div>
    </div>
  );
}
