import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Menu } from 'lucide-react';
import Logo from './navigation/Logo';
import MainNav from './navigation/MainNav';
import MobileMenu from './navigation/MobileMenu';
import HeaderAuthButtons from './auth/HeaderAuthButtons';
import HeaderMobileAuthStatus from './auth/HeaderMobileAuthStatus';

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  React.useEffect(() => {
    console.log('Header component rendered - Should include MainNav with NSW Party Planning link');
    console.log('Current location:', location.pathname);
  }, [location.pathname]);

  return (
    <header className="fixed top-0 left-0 right-0 bg-white/80 backdrop-blur-md z-50 border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6">
        <div className="flex justify-between items-center py-4">
          {/* Left side - Logo */}
          <Logo />

          {/* Center - Main Navigation */}
          <div className="flex-grow flex justify-center">
            <MainNav />
          </div>

          {/* Right side - Auth buttons and mobile menu */}
          <div className="flex items-center space-x-4">
            {/* Desktop auth buttons */}
            <div className="hidden md:block">
              <HeaderAuthButtons />
            </div>

            {/* Mobile auth status indicator */}
            <div className="md:hidden">
              <HeaderMobileAuthStatus />
            </div>

            {/* Mobile menu button - Enhanced for touch */}
            <button
              className="md:hidden btn-reactive p-3 rounded-xl hover:bg-gray-100 active:bg-gray-200 touch-target focus-enhanced min-h-[52px] min-w-[52px] flex items-center justify-center transition-all duration-200"
              aria-label="Open menu"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <Menu className="h-7 w-7 text-gray-700" />
            </button>
          </div>
        </div>
      </div>
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        currentPath={location.pathname}
      />
    </header>
  );
}
